import React, { useState } from 'react';
import { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Users, 
  Car, 
  FileText, 
  Settings, 
  BarChart3, 
  Bell,
  Search,
  Filter,
  Plus,
  Edit,
  Trash2,
  Eye,
  MoreHorizontal,
  Calendar,
  TrendingUp,
  DollarSign,
  MapPin,
  Shield,
  LogIn
} from 'lucide-react';

import { Button } from '../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Badge } from '../components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '../components/ui/dropdown-menu';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '../components/ui/form';
import { FadeInUp } from '../components/animations/FadeInUp';
import { StaggerContainer } from '../components/animations/StaggerContainer';
import { StaggerItem } from '../components/animations/StaggerItem';
import { useAuth } from '../hooks/useAuth';
import { useToast } from '../hooks/use-toast';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { 
  userService, 
  vehicleService, 
  blogService, 
  analyticsService,
  bookingService 
} from '../lib/database';
import type { User, Vehicle, BlogPost, Booking } from '../lib/database';

const loginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(1, 'Password is required'),
});

type LoginFormData = z.infer<typeof loginSchema>;

export const AdminPage = () => {
  const { user, isAdmin, loading, signIn, signOut, error: authError } = useAuth();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [showLogin, setShowLogin] = useState(false);
  const [showFallback, setShowFallback] = useState(false);
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalVehicles: 0,
    totalBookings: 0,
    revenue: '₹0'
  });
  const [users, setUsers] = useState<User[]>([]);
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [blogs, setBlogs] = useState<BlogPost[]>([]);
  const [recentBookings, setRecentBookings] = useState<Booking[]>([]);
  const [popularDestinations, setPopularDestinations] = useState<Array<{destination: string, count: number}>>([]);

  // Show fallback if loading takes too long
  useEffect(() => {
    if (loading) {
      const fallbackTimer = setTimeout(() => {
        setShowFallback(true);
      }, 6000); // Show fallback after 6 seconds

      return () => clearTimeout(fallbackTimer);
    } else {
      setShowFallback(false);
    }
  }, [loading]);

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '<EMAIL>',
      password: '',
    },
  });

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        const [
          dashboardStats,
          usersData,
          vehiclesData,
          blogsData,
          recentBookingsData,
          popularDestinationsData
        ] = await Promise.all([
          analyticsService.getDashboardStats(),
          userService.getAllUsers(),
          vehicleService.getAllVehicles(),
          blogService.getAllPosts(),
          analyticsService.getRecentBookings(),
          analyticsService.getPopularDestinations()
        ]);

        setStats({
          totalUsers: dashboardStats.totalUsers,
          totalVehicles: dashboardStats.totalVehicles,
          totalBookings: dashboardStats.totalBookings,
          revenue: '₹1,23,456' // This would be calculated from actual booking data
        });
        
        setUsers(usersData);
        setVehicles(vehiclesData);
        setBlogs(blogsData);
        setRecentBookings(recentBookingsData);
        setPopularDestinations(popularDestinationsData);
      } catch (error) {
        console.error('Error loading admin data:', error);
      }
    };

    if (isAdmin) {
      loadData();
    }
  }, [isAdmin]);

  // Show login form if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      setShowLogin(true);
    }
  }, [loading, user]);

  const handleLogin = async (data: LoginFormData) => {
    setIsLoggingIn(true);
    try {
      await signIn(data.email, data.password);
      toast({
        title: "Login Successful",
        description: "Welcome to the admin panel!",
      });
      setShowLogin(false);
    } catch (error) {
      console.error('Login error:', error);
      toast({
        title: "Login Failed",
        description: error instanceof Error ? error.message : "Invalid credentials. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoggingIn(false);
    }
  };

  const handleLogout = async () => {
    try {
      await signOut();
      toast({
        title: "Logged Out",
        description: "You have been successfully logged out.",
      });
      setShowLogin(true);
    } catch (error) {
      console.error('Logout error:', error);
      toast({
        title: "Logout Failed",
        description: "There was an error logging out. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Redirect if not admin
  if (loading) {
    return (
      <div className="min-h-screen bg-[#030711] flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="text-white text-lg">Loading Admin Panel...</div>
          {authError && (
            <div className="text-red-400 text-sm max-w-md">
              Error: {authError}
            </div>
          )}
          {showFallback && (
            <div className="space-y-3">
              <div className="text-yellow-400 text-sm">
                Taking longer than expected...
              </div>
              <div className="space-x-4">
                <button
                  onClick={() => window.location.href = '/admin/fix'}
                  className="bg-[#e9a319] text-black px-4 py-2 rounded hover:bg-[#d99100] transition-colors"
                >
                  Fix Admin Setup
                </button>
                <button
                  onClick={() => window.location.reload()}
                  className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-500 transition-colors"
                >
                  Refresh Page
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Show login form if not authenticated
  if (showLogin || (!user && !loading)) {
    return (
      <div className="min-h-screen bg-[#030711] flex items-center justify-center">
        <div className="w-full max-w-md p-4">
          <FadeInUp>
            <Card className="bg-[#060e23] border-[#1f293780]">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-amber-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-2xl font-bold text-white">
                  Admin Login
                </CardTitle>
                <p className="text-gray-400">
                  Sign in to access the admin panel
                </p>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="bg-[#1f293780] rounded-lg p-4 space-y-3">
                  <h3 className="text-white font-medium flex items-center">
                    <LogIn className="w-4 h-4 mr-2 text-[#e9a319]" />
                    Default Credentials
                  </h3>
                  <div className="space-y-2 text-sm text-gray-300">
                    <div className="flex justify-between">
                      <span>Email:</span>
                      <span className="text-[#e9a319]"><EMAIL></span>
                    </div>
                    <div className="flex justify-between">
                      <span>Password:</span>
                      <span className="text-[#e9a319]">admin@123</span>
                    </div>
                  </div>
                </div>

                {authError && (
                  <div className="bg-red-900/20 border border-red-700/30 rounded-lg p-4">
                    <div className="flex items-start space-x-3">
                      <div className="text-sm text-red-300">
                        <p className="font-medium mb-1">Authentication Error:</p>
                        <p className="text-xs">{authError}</p>
                      </div>
                    </div>
                  </div>
                )}

                <Form {...form}>
                  <form onSubmit={form.handleSubmit(handleLogin)} className="space-y-4">
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-gray-300">Email</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter email address"
                              className="bg-[#030711] border-gray-800 text-white"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-gray-300">Password</FormLabel>
                          <FormControl>
                            <Input
                              type="password"
                              placeholder="Enter password"
                              className="bg-[#030711] border-gray-800 text-white"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Button
                      type="submit"
                      disabled={isLoggingIn}
                      className="w-full bg-[#e9a319] text-black hover:bg-[#d99100] h-12"
                    >
                      {isLoggingIn ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-black border-t-transparent rounded-full mr-2"
                        />
                      ) : (
                        <LogIn className="w-5 h-5 mr-2" />
                      )}
                      {isLoggingIn ? 'Signing In...' : 'Sign In'}
                    </Button>
                  </form>
                </Form>

                <div className="text-center">
                  <Button
                    variant="ghost"
                    className="text-gray-400 hover:text-white"
                    onClick={() => window.location.href = '/admin/setup'}
                  >
                    Need to setup admin? Click here
                  </Button>
                </div>
              </CardContent>
            </Card>
          </FadeInUp>
        </div>
      </div>
    );
  }

  // Check if user is admin after login
  if (user && !isAdmin) {
    return (
      <div className="min-h-screen bg-[#030711] flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Access Denied</h1>
          <p className="text-gray-400 mb-4">You don't have admin permissions to access this page.</p>
          <Button
            onClick={() => window.location.href = '/'}
            className="bg-[#e9a319] text-black hover:bg-[#d99100]"
          >
            Go to Home
          </Button>
        </div>
      </div>
    );
  }

  const statsData = [
    {
      title: 'Total Users',
      value: stats.totalUsers.toString(),
      change: '+12%',
      icon: Users,
      color: 'text-blue-500'
    },
    {
      title: 'Active Vehicles',
      value: stats.totalVehicles.toString(),
      change: '+3%',
      icon: Car,
      color: 'text-green-500'
    },
    {
      title: 'Total Bookings',
      value: stats.totalBookings.toString(),
      change: '+18%',
      icon: Calendar,
      color: 'text-purple-500'
    },
    {
      title: 'Revenue',
      value: stats.revenue,
      change: '+25%',
      icon: DollarSign,
      color: 'text-yellow-500'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'available':
      case 'published':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'inactive':
      case 'maintenance':
      case 'draft':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'booked':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-[#030711]">
      {/* Header */}
      <header className="bg-[#060e23] border-b border-[#1f293780] sticky top-0 z-50">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-yellow-400 to-amber-600 flex items-center justify-center">
                <span className="text-white font-bold text-lg">B</span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">Admin Panel</h1>
                <p className="text-sm text-gray-400">Bhoomi Tour & Travels</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  placeholder="Search..."
                  className="pl-10 w-64 bg-[#030711] border-gray-800 text-white"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white">
                <Bell className="w-5 h-5" />
              </Button>
              <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white">
                <Settings className="w-5 h-5" />
              </Button>
              <Button 
                variant="ghost" 
                onClick={handleLogout}
                className="text-gray-400 hover:text-white"
              >
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex">
        {/* Sidebar */}
        <aside className="w-64 bg-[#060e23] border-r border-[#1f293780] min-h-[calc(100vh-80px)]">
          <nav className="p-4">
            <div className="space-y-2">
              {[
                { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
                { id: 'users', label: 'Users', icon: Users },
                { id: 'vehicles', label: 'Vehicles', icon: Car },
                { id: 'blogs', label: 'Blogs', icon: FileText },
              ].map((item) => (
                <Button
                  key={item.id}
                  variant={activeTab === item.id ? 'default' : 'ghost'}
                  className={`w-full justify-start ${
                    activeTab === item.id 
                      ? 'bg-[#e9a319] text-black hover:bg-[#d99100]' 
                      : 'text-gray-400 hover:text-white hover:bg-[#1f293780]'
                  }`}
                  onClick={() => setActiveTab(item.id)}
                >
                  <item.icon className="w-4 h-4 mr-3" />
                  {item.label}
                </Button>
              ))}
            </div>
          </nav>
        </aside>

        {/* Content Area */}
        <main className="flex-1 p-6 overflow-hidden">
          <AnimatePresence mode="wait">
            {activeTab === 'dashboard' && (
              <motion.div
                key="dashboard"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                <div>
                  <h2 className="text-2xl font-bold text-white mb-2">Dashboard</h2>
                  <p className="text-gray-400">Overview of your business metrics</p>
                </div>

                {/* Stats Grid */}
                <StaggerContainer className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {statsData.map((stat, index) => (
                    <StaggerItem key={index}>
                      <Card className="bg-[#060e23] border-[#1f293780] hover:border-[#e9a319]/30 transition-all duration-300">
                        <CardContent className="p-6">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm text-gray-400 mb-1">{stat.title}</p>
                              <p className="text-2xl font-bold text-white">{stat.value}</p>
                              <p className="text-sm text-green-400">{stat.change} from last month</p>
                            </div>
                            <div className={`p-3 rounded-lg bg-[#1f293780] ${stat.color}`}>
                              <stat.icon className="w-6 h-6" />
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </StaggerItem>
                  ))}
                </StaggerContainer>

                {/* Recent Activity */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card className="bg-[#060e23] border-[#1f293780]">
                    <CardHeader>
                      <CardTitle className="text-white">Recent Bookings</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {recentBookings.slice(0, 3).map((booking, index) => (
                        <div key={booking.id} className="flex items-center space-x-3 p-3 rounded-lg bg-[#1f293780]">
                          <div className="w-10 h-10 rounded-full bg-[#e9a319] flex items-center justify-center">
                            <Car className="w-5 h-5 text-black" />
                          </div>
                          <div className="flex-1">
                            <p className="text-white font-medium">Booking #{booking.id?.slice(-6) || 'N/A'}</p>
                            <p className="text-sm text-gray-400">{booking.pickup_location} to {booking.dropoff_location}</p>
                          </div>
                          <Badge className={getStatusColor(booking.status || 'pending')}>
                            {booking.status}
                          </Badge>
                        </div>
                      ))}
                    </CardContent>
                  </Card>

                  <Card className="bg-[#060e23] border-[#1f293780]">
                    <CardHeader>
                      <CardTitle className="text-white">Popular Destinations</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {popularDestinations.slice(0, 3).map((destination, index) => (
                        <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-[#1f293780]">
                          <div className="flex items-center space-x-3">
                            <MapPin className="w-5 h-5 text-[#e9a319]" />
                            <span className="text-white">{destination.destination}</span>
                          </div>
                          <Badge variant="outline" className="text-gray-400 border-gray-600">
                            {destination.count} trips
                          </Badge>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                </div>
              </motion.div>
            )}

            {activeTab === 'users' && (
              <motion.div
                key="users"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-2xl font-bold text-white mb-2">Users Management</h2>
                    <p className="text-gray-400">Manage your customers and their information</p>
                  </div>
                  <Button className="bg-[#e9a319] text-black hover:bg-[#d99100]">
                    <Plus className="w-4 h-4 mr-2" />
                    Add User
                  </Button>
                </div>

                <Card className="bg-[#060e23] border-[#1f293780]">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-white">All Users</CardTitle>
                      <div className="flex items-center space-x-2">
                        <Button variant="outline" size="sm" className="text-gray-400 border-gray-600">
                          <Filter className="w-4 h-4 mr-2" />
                          Filter
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="overflow-hidden">
                      <div className="space-y-4">
                        {users.map((userData) => (
                          <motion.div
                            key={userData.id}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            className="flex items-center justify-between p-4 rounded-lg bg-[#1f293780] hover:bg-[#2a3441] transition-colors"
                          >
                            <div className="flex items-center space-x-4">
                              <img
                                src="https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop"
                                alt={userData.name}
                                className="w-12 h-12 rounded-full object-cover"
                              />
                              <div>
                                <h3 className="text-white font-medium">{userData.name}</h3>
                                <p className="text-sm text-gray-400">{userData.phone || 'No phone'}</p>
                                <p className="text-sm text-gray-400">Role: {userData.role}</p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-4">
                              <div className="text-right">
                                <p className="text-white font-medium">User ID: {userData.id?.slice(-6) || 'N/A'}</p>
                                <p className="text-sm text-gray-400">
                                  Joined {new Date(userData.created_at || '').toLocaleDateString()}
                                </p>
                              </div>
                              <Badge className="bg-green-100 text-green-800 border-green-200">
                                active
                              </Badge>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" className="text-gray-400">
                                    <MoreHorizontal className="w-4 h-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent className="bg-[#060e23] border-gray-700">
                                  <DropdownMenuItem className="text-gray-300 hover:text-white">
                                    <Eye className="w-4 h-4 mr-2" />
                                    View Details
                                  </DropdownMenuItem>
                                  <DropdownMenuItem className="text-gray-300 hover:text-white">
                                    <Edit className="w-4 h-4 mr-2" />
                                    Edit User
                                  </DropdownMenuItem>
                                  <DropdownMenuItem className="text-red-400 hover:text-red-300">
                                    <Trash2 className="w-4 h-4 mr-2" />
                                    Delete User
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {activeTab === 'vehicles' && (
              <motion.div
                key="vehicles"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-2xl font-bold text-white mb-2">Vehicle Management</h2>
                    <p className="text-gray-400">Manage your fleet and vehicle information</p>
                  </div>
                  <Button className="bg-[#e9a319] text-black hover:bg-[#d99100]">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Vehicle
                  </Button>
                </div>

                <Card className="bg-[#060e23] border-[#1f293780]">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-white">Fleet Overview</CardTitle>
                      <div className="flex items-center space-x-2">
                        <Button variant="outline" size="sm" className="text-gray-400 border-gray-600">
                          <Filter className="w-4 h-4 mr-2" />
                          Filter
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {vehicles.map((vehicle) => (
                        <motion.div
                          key={vehicle.id}
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          className="bg-[#1f293780] rounded-lg overflow-hidden hover:bg-[#2a3441] transition-colors"
                        >
                          <div className="relative h-48">
                            <img
                              src={vehicle.image_url || 'https://images.pexels.com/photos/1545743/pexels-photo-1545743.jpeg?auto=compress&cs=tinysrgb&w=300&h=200&fit=crop'}
                              alt={vehicle.name}
                              className="w-full h-full object-cover"
                            />
                            <div className="absolute top-3 right-3">
                              <Badge className={getStatusColor(vehicle.available ? 'available' : 'unavailable')}>
                                {vehicle.available ? 'available' : 'unavailable'}
                              </Badge>
                            </div>
                          </div>
                          <div className="p-4">
                            <div className="flex items-center justify-between mb-2">
                              <h3 className="text-white font-medium">{vehicle.name}</h3>
                              <Badge variant="outline" className="text-gray-400 border-gray-600">
                                {vehicle.type}
                              </Badge>
                            </div>
                            <div className="space-y-2 text-sm text-gray-400">
                              <p>Capacity: {vehicle.capacity} passengers</p>
                              <p>Type: {vehicle.type}</p>
                              <p>Features: {vehicle.features?.length || 0} included</p>
                              <p className="text-[#e9a319] font-medium">₹{vehicle.price_per_km}/km</p>
                            </div>
                            <div className="flex items-center justify-between mt-4">
                              <Button variant="outline" size="sm" className="text-gray-400 border-gray-600">
                                <Eye className="w-4 h-4 mr-2" />
                                View
                              </Button>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" className="text-gray-400">
                                    <MoreHorizontal className="w-4 h-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent className="bg-[#060e23] border-gray-700">
                                  <DropdownMenuItem className="text-gray-300 hover:text-white">
                                    <Edit className="w-4 h-4 mr-2" />
                                    Edit Vehicle
                                  </DropdownMenuItem>
                                  <DropdownMenuItem className="text-red-400 hover:text-red-300">
                                    <Trash2 className="w-4 h-4 mr-2" />
                                    Delete Vehicle
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {activeTab === 'blogs' && (
              <motion.div
                key="blogs"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-2xl font-bold text-white mb-2">Blog Management</h2>
                    <p className="text-gray-400">Manage your blog posts and content</p>
                  </div>
                  <Button className="bg-[#e9a319] text-black hover:bg-[#d99100]">
                    <Plus className="w-4 h-4 mr-2" />
                    New Post
                  </Button>
                </div>

                <Card className="bg-[#060e23] border-[#1f293780]">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-white">All Posts</CardTitle>
                      <div className="flex items-center space-x-2">
                        <Button variant="outline" size="sm" className="text-gray-400 border-gray-600">
                          <Filter className="w-4 h-4 mr-2" />
                          Filter
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {blogs.map((blog) => (
                        <motion.div
                          key={blog.id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          className="flex items-center space-x-4 p-4 rounded-lg bg-[#1f293780] hover:bg-[#2a3441] transition-colors"
                        >
                          <img
                            src={blog.image_url || 'https://images.pexels.com/photos/1591373/pexels-photo-1591373.jpeg?auto=compress&cs=tinysrgb&w=300&h=200&fit=crop'}
                            alt={blog.title}
                            className="w-20 h-20 rounded-lg object-cover flex-shrink-0"
                          />
                          <div className="flex-1 min-w-0">
                            <h3 className="text-white font-medium truncate">{blog.title}</h3>
                            <div className="flex items-center space-x-4 mt-1">
                              <p className="text-sm text-gray-400">By {blog.author}</p>
                              <Badge variant="outline" className="text-gray-400 border-gray-600">
                                {blog.category}
                              </Badge>
                            </div>
                            <div className="flex items-center space-x-4 mt-2">
                              <p className="text-sm text-gray-400">
                                {blog.published_at ? new Date(blog.published_at).toLocaleDateString() : 'Not published'}
                              </p>
                              <p className="text-sm text-gray-400">ID: {blog.id?.slice(-6) || 'N/A'}</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-3">
                            <Badge className={getStatusColor(blog.status || 'draft')}>
                              {blog.status || 'draft'}
                            </Badge>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon" className="text-gray-400">
                                  <MoreHorizontal className="w-4 h-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent className="bg-[#060e23] border-gray-700">
                                <DropdownMenuItem className="text-gray-300 hover:text-white">
                                  <Eye className="w-4 h-4 mr-2" />
                                  View Post
                                </DropdownMenuItem>
                                <DropdownMenuItem className="text-gray-300 hover:text-white">
                                  <Edit className="w-4 h-4 mr-2" />
                                  Edit Post
                                </DropdownMenuItem>
                                <DropdownMenuItem className="text-red-400 hover:text-red-300">
                                  <Trash2 className="w-4 h-4 mr-2" />
                                  Delete Post
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </AnimatePresence>
        </main>
      </div>
    </div>
  );
};