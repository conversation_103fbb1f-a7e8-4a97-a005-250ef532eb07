import React from "react";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { But<PERSON> } from "../../../../components/ui/button";
import { Card, CardContent } from "../../../../components/ui/card";
import { Input } from "../../../../components/ui/input";
import <PERSON><PERSON>ogo from "../../../../assests/BrandLogo.png";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "../../../../components/ui/select";
import { FadeInUp } from "../../../../components/animations/FadeInUp";
import { SlideInLeft } from "../../../../components/animations/SlideInLeft";
import { SlideInRight } from "../../../../components/animations/SlideInRight";
import { ScaleOnHover } from "../../../../components/animations/ScaleOnHover";

export const HeroSection = (): JSX.Element => {
  const navigate = useNavigate();

  // Feature items data
  const features = [
    {
      title: "24/7 Service",
      icon: "https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=32&h=32&fit=crop",
    },
    {
      title: "GPS Tracking",
      icon: "https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=32&h=32&fit=crop",
    },
    {
      title: "Professional Drivers",
      icon: "https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=32&h=32&fit=crop",
    },
  ];

  const handleBookNow = () => {
    navigate('/book/economy');
  };

  const handleFindCab = () => {
    navigate('/book/economy');
  };

  return (
    <section className="relative w-full min-h-[600px] sm:min-h-[700px] lg:min-h-[900px] overflow-x-hidden prevent-scroll-interference">
      <div className="relative h-full">
        {/* Background elements */}
        <div className="absolute inset-0 w-full h-full">
          <div className="absolute w-full h-full">
            <div className="flex w-full h-full items-start justify-end overflow-x-hidden">
              <motion.div
                initial={{ scale: 1.1, opacity: 0 }}
                animate={{ scale: 1, opacity: 0.8 }}
                transition={{
                  duration: 2,
                  ease: [0.25, 0.46, 0.45, 0.94],
                  type: "spring",
                  stiffness: 50,
                  damping: 20
                }}
                className="relative w-full max-w-[800px] h-full rounded-[13.2px]"
              >
                <img 
                  src="https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=800&h=900&fit=crop"
                  alt="Hero background"
                  className="w-full h-full object-cover rounded-[13.2px]"
                />
              </motion.div>
              <div className="absolute inset-0 bg-[linear-gradient(270deg,rgba(0,0,0,0)_0%,rgba(3,7,17,1)_100%)]" />
            </div>
          </div>
        </div>

        {/* Scroll indicator */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            delay: 2.5,
            duration: 1,
            ease: [0.25, 0.46, 0.45, 0.94],
            type: "spring",
            stiffness: 100,
            damping: 15
          }}
          className="absolute bottom-10 left-1/2 transform -translate-x-1/2 lg:bottom-20 lg:left-auto lg:right-1/2 lg:transform-none"
        >
          <div className="flex flex-col items-center">
            <div className="text-gray-400 text-sm mb-2">Scroll to explore</div>
            <motion.svg
              className="w-5 h-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              animate={{ y: [0, 8, 0] }}
              transition={{
                duration: 2.5,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </motion.svg>
          </div>
        </motion.div>

        <div className="container mx-auto px-4 py-8 lg:py-16 relative z-10">
          <div className="flex flex-col lg:flex-row items-center lg:items-start gap-8 lg:gap-16">
            {/* Left side content */}
            <div className="w-full lg:w-1/2 text-center lg:text-left order-2 lg:order-1">
              {/* Logo and company name */}
              <SlideInLeft delay={0.2}>
                <div className="flex items-center justify-center lg:justify-start mb-6 lg:mb-8">
                  <ScaleOnHover>
                    <div className="w-16 h-16 lg:w-20 lg:h-20 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 flex items-center justify-center mr-4 p-2">
                      <img
                        src={BrandLogo}
                        alt="Bhoomi Tour & Travels Logo"
                        className="w-full h-full object-contain"
                      />
                    </div>
                  </ScaleOnHover>
                  <div className="text-xl lg:text-2xl font-bold text-white">
                    Bhoomi Tour & Travels
                  </div>
                </div>
              </SlideInLeft>

              {/* Main heading */}
              <SlideInLeft delay={0.4}>
                <div className="mb-6 lg:mb-8">
                  <h1 className="text-3xl sm:text-4xl lg:text-6xl font-bold text-white leading-tight mb-4">
                    <motion.span
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{
                        delay: 0.8,
                        duration: 1,
                        ease: [0.25, 0.46, 0.45, 0.94],
                        type: "spring",
                        stiffness: 100,
                        damping: 15
                      }}
                    >
                      Book Your
                    </motion.span>
                    <br />
                    <motion.span
                      className="bg-gradient-to-r from-yellow-400 to-amber-600 bg-clip-text text-transparent"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{
                        delay: 1.1,
                        duration: 1,
                        ease: [0.25, 0.46, 0.45, 0.94],
                        type: "spring",
                        stiffness: 100,
                        damping: 15
                      }}
                    >
                      Premium
                    </motion.span>{" "}
                    <motion.span
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{
                        delay: 1.4,
                        duration: 1,
                        ease: [0.25, 0.46, 0.45, 0.94],
                        type: "spring",
                        stiffness: 100,
                        damping: 15
                      }}
                    >
                      Cab
                    </motion.span>
                    <br />
                    <motion.span
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{
                        delay: 1.7,
                        duration: 1,
                        ease: [0.25, 0.46, 0.45, 0.94],
                        type: "spring",
                        stiffness: 100,
                        damping: 15
                      }}
                    >
                      In Minutes
                    </motion.span>
                  </h1>
                </div>
              </SlideInLeft>

              {/* Description text */}
              <SlideInLeft delay={0.6}>
                <div className="mb-8 lg:mb-12">
                  <p className="text-lg lg:text-xl leading-7 text-gray-400 max-w-lg mx-auto lg:mx-0">
                    Experience the ultimate in comfort and reliability with
                    our premium cab booking service. Fast, safe, and
                    always on time.
                  </p>
                </div>
              </SlideInLeft>

              {/* CTA Buttons */}
              <SlideInLeft delay={0.8}>
                <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8 lg:mb-12">
                  <ScaleOnHover>
                    <Button 
                      className="h-11 bg-[#fad089] text-[#030711] rounded-[10px] shadow-[0px_0px_0px_transparent,0px_0px_0px_transparent,0px_4px_6px_-1px_#feb4354c,0px_2px_4px_-1px_#fad08926] px-6 hover:shadow-lg transition-all duration-300"
                      onClick={handleBookNow}
                    >
                      Book Now
                    </Button>
                  </ScaleOnHover>
                  <ScaleOnHover>
                    <Button
                      variant="outline"
                      className="h-11 bg-[#030711] text-gray-50 rounded-[10px] border-[#8e919666] px-6 hover:bg-white/10 transition-all duration-300"
                      onClick={() => document.getElementById('services')?.scrollIntoView({ behavior: 'smooth' })}
                    >
                      Our Services
                    </Button>
                  </ScaleOnHover>
                </div>
              </SlideInLeft>

              {/* Features row */}
              <SlideInLeft delay={1}>
                <div className="flex flex-wrap justify-center lg:justify-start gap-4 lg:gap-8">
                  {features.map((feature, index) => (
                    <motion.div
                      key={index}
                      className="flex items-center"
                      initial={{ opacity: 0, x: -30 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{
                        delay: 2.2 + index * 0.3,
                        duration: 0.8,
                        ease: [0.25, 0.46, 0.45, 0.94],
                        type: "spring",
                        stiffness: 100,
                        damping: 15
                      }}
                      whileHover={{
                        scale: 1.05,
                        transition: { type: "spring", stiffness: 400, damping: 25 }
                      }}
                    >
                      <div className="w-10 h-10 bg-[#e9a31933] rounded-full flex items-center justify-center mr-3">
                        <img
                          className="w-5 h-5 rounded-full object-cover"
                          alt={`${feature.title} icon`}
                          src={feature.icon}
                        />
                      </div>
                      <div className="text-base font-medium text-gray-50 whitespace-nowrap">
                        {feature.title}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </SlideInLeft>
            </div>

            {/* Booking form card */}
            <div className="w-full lg:w-1/2 max-w-md mx-auto lg:mx-0 order-1 lg:order-2">
              <SlideInRight delay={0.4}>
                <motion.div
                  whileHover={{
                    y: -8,
                    transition: { type: "spring", stiffness: 400, damping: 25 }
                  }}
                  transition={{ type: "spring", stiffness: 400, damping: 25 }}
                >
                  <Card className="bg-[#1a1f2c] rounded-2xl shadow-[0px_0px_0px_transparent,0px_0px_0px_transparent,0px_25px_50px_-12px_#00000040] p-6 lg:p-8 backdrop-blur-sm border border-white/10">
                    <CardContent className="p-0">
                      <h2 className="font-bold text-gray-50 text-xl mb-6">
                        Find Your Ride
                      </h2>

                      <div className="space-y-4">
                        {/* Pickup Location */}
                        <motion.div
                          className="space-y-2"
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{
                            delay: 1.0,
                            duration: 0.8,
                            ease: [0.25, 0.46, 0.45, 0.94],
                            type: "spring",
                            stiffness: 100,
                            damping: 15
                          }}
                        >
                          <label className="font-medium text-gray-50 text-base">
                            Pickup Location
                          </label>
                          <div className="relative">
                            <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <Input
                              className="pl-10 bg-[#060e23] border-gray-800 text-gray-50 text-sm h-10 focus:border-[#e9a319] transition-colors"
                              placeholder="Enter pickup address"
                            />
                          </div>
                        </motion.div>

                        {/* Dropoff Location */}
                        <motion.div
                          className="space-y-2"
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{
                            delay: 1.3,
                            duration: 0.8,
                            ease: [0.25, 0.46, 0.45, 0.94],
                            type: "spring",
                            stiffness: 100,
                            damping: 15
                          }}
                        >
                          <label className="font-medium text-gray-50 text-base">
                            Dropoff Location
                          </label>
                          <div className="relative">
                            <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <Input
                              className="pl-10 bg-[#060e23] border-gray-800 text-gray-50 text-sm h-10 focus:border-[#e9a319] transition-colors"
                              placeholder="Enter destination address"
                            />
                          </div>
                        </motion.div>

                        {/* Date and Passengers row */}
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                          {/* Date */}
                          <motion.div
                            className="space-y-2"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{
                              delay: 1.6,
                              duration: 0.8,
                              ease: [0.25, 0.46, 0.45, 0.94],
                              type: "spring",
                              stiffness: 100,
                              damping: 15
                            }}
                          >
                            <label className="font-medium text-gray-50 text-base">
                              Date
                            </label>
                            <Input
                              type="date"
                              className="bg-[#060e23] border-gray-800 text-gray-50 text-sm h-10 focus:border-[#e9a319] transition-colors"
                            />
                          </motion.div>

                          {/* Passengers */}
                          <motion.div
                            className="space-y-2"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{
                              delay: 1.9,
                              duration: 0.8,
                              ease: [0.25, 0.46, 0.45, 0.94],
                              type: "spring",
                              stiffness: 100,
                              damping: 15
                            }}
                          >
                            <label className="font-medium text-gray-50 text-base">
                              Passengers
                            </label>
                            <div className="relative">
                              <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                              </svg>
                              <Select>
                                <SelectTrigger className="pl-10 bg-[#060e23] border-gray-800 text-gray-50 h-10 focus:border-[#e9a319]">
                                  <SelectValue placeholder="1 Passenger" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="1">1 Passenger</SelectItem>
                                  <SelectItem value="2">2 Passengers</SelectItem>
                                  <SelectItem value="3">3 Passengers</SelectItem>
                                  <SelectItem value="4">4 Passengers</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </motion.div>
                        </div>

                        {/* Cab Type */}
                        <motion.div
                          className="space-y-2"
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{
                            delay: 2.2,
                            duration: 0.8,
                            ease: [0.25, 0.46, 0.45, 0.94],
                            type: "spring",
                            stiffness: 100,
                            damping: 15
                          }}
                        >
                          <label className="font-medium text-gray-50 text-sm">
                            Cab Type
                          </label>
                          <Select>
                            <SelectTrigger className="bg-[#060e23] border-gray-800 text-gray-50 h-10 focus:border-[#e9a319]">
                              <SelectValue placeholder="Economy" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="economy">Economy</SelectItem>
                              <SelectItem value="premium">Premium</SelectItem>
                              <SelectItem value="luxury">Luxury</SelectItem>
                              <SelectItem value="traveller">Traveller</SelectItem>
                            </SelectContent>
                          </Select>
                        </motion.div>

                        {/* Find My Cab button */}
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{
                            delay: 2.5,
                            duration: 0.8,
                            ease: [0.25, 0.46, 0.45, 0.94],
                            type: "spring",
                            stiffness: 100,
                            damping: 15
                          }}
                        >
                          <ScaleOnHover>
                            <Button 
                              className="w-full h-10 bg-[#fad089] text-[#030711] rounded-[10px] shadow-[0px_0px_0px_transparent,0px_0px_0px_transparent,0px_4px_6px_-1px_#feb4354c,0px_2px_4px_-1px_#fad08926] mt-6 hover:shadow-lg transition-all duration-300"
                              onClick={handleFindCab}
                            >
                              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                              </svg>
                              Find My Cab
                            </Button>
                          </ScaleOnHover>
                        </motion.div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </SlideInRight>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};