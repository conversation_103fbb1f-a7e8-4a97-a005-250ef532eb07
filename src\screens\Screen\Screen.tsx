import { PhoneCallIcon, SunIcon, MenuIcon } from "lucide-react";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import BrandLogo from "../../assests/BrandLogo.png";
import { Button } from "../../components/ui/button";
import { ChooseYourRideSection } from "./sections/ChooseYourRideSection";
import { ContactSection } from "./sections/ContactSection";
import { FooterSection } from "./sections/FooterSection";
import { HeroSection } from "./sections/HeroSection";
import { HowItWorksSection } from "./sections/HowItWorksSection";
import { PremiumServicesSection } from "./sections/PremiumServicesSection";
import { TestimonialsSection } from "./sections/TestimonialsSection";
import { FadeInUp } from "../../components/animations/FadeInUp";
import { ScaleOnHover } from "../../components/animations/ScaleOnHover";

export const Screen = (): JSX.Element => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const navigate = useNavigate();

  // Navigation menu items data
  const navItems = [
    { label: "Home", active: true, href: "#home" },
    { label: "Services", active: false, href: "#services" },
    { label: "How It Works", active: false, href: "#how-it-works" },
    { label: "Testimonials", active: false, href: "#testimonials" },
    { label: "Blog", active: false, href: "#blog" },
    { label: "Contact", active: false, href: "#contact" },
  ];

  const scrollToSection = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setMobileMenuOpen(false);
  };

  const handleBookNow = () => {
    navigate('/book/economy');
  };

  return (
    <div className="w-full min-h-screen bg-[#030711] overflow-x-hidden prevent-scroll-interference">
      <div className="relative">
        {/* Header/Navigation */}
        <motion.header 
          initial={{ y: -100 }}
          animate={{ y: 0 }}
          transition={{ duration: 0.6, ease: [0.21, 0.47, 0.32, 0.98] }}
          className="w-full h-[55px] bg-[#03071199] border-b border-[#ffffff0d] shadow-[0px_4px_6px_-1px_#0000001a,0px_2px_4px_-2px_#0000001a] sticky top-0 z-50 backdrop-blur-md"
        >
          <div className="container mx-auto flex items-center justify-between h-full px-4 max-w-full">
            {/* Logo */}
            <motion.div
              className="flex items-center flex-shrink-0"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
            >
              <img
                src={BrandLogo}
                alt="Bhoomi Tour & Travels Logo"
                className="w-9 h-9 rounded-[10px] object-contain"
              />
              <div className="ml-2 font-bold text-sm sm:text-lg text-white truncate">
                Bhoomi Tour & Travels
              </div>
            </motion.div>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-2">
              {navItems.map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 + 0.3 }}
                >
                  <Button
                    variant="ghost"
                    className="h-[38px] bg-[#03071133] rounded-[10px] border border-solid border-[#1f29374c] text-gray-400 font-medium text-sm px-3 hover:text-white hover:bg-[#1f29374c] transition-all duration-300"
                    onClick={() => scrollToSection(item.href)}
                  >
                    {item.label}
                  </Button>
                </motion.div>
              ))}
            </nav>

            {/* Contact and Actions */}
            <div className="flex items-center space-x-2 sm:space-x-4">
              {/* Desktop Contact Button */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 }}
              >
                <Button
                  variant="outline"
                  className="hidden sm:flex h-[38px] bg-[#03071133] rounded-[10px] border border-solid border-[#1f29374c] text-[#e9a319] font-medium text-xs sm:text-sm px-2 sm:px-4 hover:bg-[#e9a319] hover:text-black transition-all duration-300"
                >
                  <PhoneCallIcon className="w-4 h-4 mr-1 sm:mr-2" />
                  <span className="hidden md:inline">Call Now</span>
                  <span className="ml-1">+91 79994 75781</span>
                </Button>
              </motion.div>

              {/* Mobile Contact Button */}
              <Button
                variant="outline"
                className="sm:hidden h-[38px] bg-[#03071133] rounded-[10px] border border-solid border-[#1f29374c] text-[#e9a319] font-medium text-xs px-2 hover:bg-[#e9a319] hover:text-black transition-all duration-300"
              >
                <PhoneCallIcon className="w-4 h-4" />
              </Button>

              <div className="flex items-center">
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-6 h-6 rounded-full hidden sm:flex hover:bg-white/10 transition-all duration-300"
                >
                  <SunIcon className="w-4 h-4" />
                  <span className="sr-only">Toggle theme</span>
                </Button>

                <ScaleOnHover>
                  <Button 
                    className="ml-2 h-9 rounded-full bg-[linear-gradient(90deg,rgba(233,163,25,1)_0%,rgba(250,208,137,1)_100%),linear-gradient(0deg,rgba(233,163,25,1)_0%,rgba(233,163,25,1)_100%)] text-white font-medium text-xs sm:text-sm px-3 sm:px-4 hover:shadow-lg transition-all duration-300"
                    onClick={handleBookNow}
                  >
                    Book Now
                  </Button>
                </ScaleOnHover>

                {/* Mobile Menu Button */}
                <Button
                  variant="ghost"
                  size="icon"
                  className="lg:hidden ml-2 w-8 h-8 hover:bg-white/10 transition-all duration-300"
                  onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                >
                  <motion.div
                    animate={{ rotate: mobileMenuOpen ? 90 : 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <MenuIcon className="w-5 h-5 text-white" />
                  </motion.div>
                </Button>
              </div>
            </div>
          </div>

          {/* Mobile Menu */}
          <AnimatePresence>
            {mobileMenuOpen && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
                className="lg:hidden absolute top-full left-0 w-full bg-[#030711] border-b border-[#ffffff0d] shadow-lg z-40 backdrop-blur-md"
              >
                <nav className="container mx-auto px-4 py-4">
                  <div className="flex flex-col space-y-2">
                    {navItems.map((item, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <Button
                          variant="ghost"
                          className="justify-start h-[38px] bg-[#03071133] rounded-[10px] border border-solid border-[#1f29374c] text-gray-400 font-medium hover:text-white hover:bg-[#1f29374c] transition-all duration-300"
                          onClick={() => scrollToSection(item.href)}
                        >
                          {item.label}
                        </Button>
                      </motion.div>
                    ))}
                    
                    {/* Admin Setup Link - Hidden in production */}
                    <motion.div
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.8 }}
                    >
                      <Button
                        variant="ghost"
                        className="justify-start h-[38px] bg-[#03071133] rounded-[10px] border border-solid border-[#1f29374c] text-gray-400 font-medium text-sm px-3 hover:text-white hover:bg-[#1f29374c] transition-all duration-300"
                        onClick={() => window.location.href = '/admin/setup'}
                      >
                        Admin Setup
                      </Button>
                    </motion.div>
                  </div>
                </nav>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.header>

        {/* Main Content Sections */}
        <main className="w-full overflow-x-hidden prevent-scroll-interference">
          {/* Hero Section */}
          <section id="home">
            <HeroSection />
          </section>

          {/* Choose Your Ride Section */}
          <section id="services">
            <ChooseYourRideSection />
          </section>

          {/* How It Works Section */}
          <section id="how-it-works">
            <HowItWorksSection />
          </section>

          {/* Testimonials Section */}
          <section id="testimonials">
            <TestimonialsSection />
          </section>

          {/* Premium Services Section */}
          <section id="blog">
            <PremiumServicesSection />
          </section>

          {/* Contact Section */}
          <section id="contact">
            <ContactSection />
          </section>

          {/* Footer Section */}
          <section>
            <FooterSection />
          </section>
        </main>

        {/* WhatsApp Button */}
        <motion.div
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ delay: 1, type: "spring", stiffness: 200, damping: 20 }}
          className="fixed bottom-4 right-4 sm:bottom-10 sm:right-10 z-50"
        >
          <ScaleOnHover scale={1.1}>
            <Button className="w-12 h-12 sm:w-14 sm:h-14 rounded-full bg-green-500 shadow-[0px_10px_15px_-3px_#0000001a,0px_4px_6px_-4px_#0000001a] p-0 hover:bg-green-600 transition-colors">
              <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.700"/>
              </svg>
            </Button>
          </ScaleOnHover>
        </motion.div>
      </div>
    </div>
  );
};