@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .all-\[unset\] {
    all: unset;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;

    --card: transparent;
    --card-foreground: 222.2 47.4% 11.2%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;

    --ring: 215 20.2% 65.1%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;

    --muted: 223 47% 11%;
    --muted-foreground: 215.4 16.3% 56.9%;

    --accent: 216 34% 17%;
    --accent-foreground: 210 40% 98%;

    --popover: 224 71% 4%;
    --popover-foreground: 215 20.2% 65.1%;

    --border: 216 34% 17%;
    --input: 216 34% 17%;

    --card: transparent;
    --card-foreground: 213 31% 91%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 1.2%;

    --secondary: 222.2 47.4% 11.2%;
    --secondary-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --ring: 216 34% 17%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html, body {
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* Fix scroll interference issues */
  * {
    scroll-behavior: smooth;
  }

  /* Prevent components from interfering with main scroll */
  .prevent-scroll-interference {
    touch-action: pan-y;
    overscroll-behavior: contain;
  }

  /* Animation performance optimizations */
  * {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Hardware acceleration for animations */
  [data-framer-motion] {
    will-change: transform, opacity;
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* Smooth transitions for all elements */
  * {
    transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  /* Optimize button and interactive element animations */
  button, .button, [role="button"] {
    transform: translateZ(0);
    backface-visibility: hidden;
    will-change: transform, opacity;
  }

  /* Optimize image animations */
  img {
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* Rotating light outline effect for cards */
  .rotating-light-outline {
    position: relative;
  }

  .rotating-light-outline::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: conic-gradient(
      from 0deg,
      transparent,
      rgba(233, 163, 25, 0.4),
      rgba(233, 163, 25, 0.8),
      rgba(233, 163, 25, 0.4),
      transparent
    );
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
    animation: rotate-light 3s linear infinite;
    z-index: -1;
  }

  .rotating-light-outline:hover::before {
    opacity: 1;
  }

  @keyframes rotate-light {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  /* Ensure the card content stays above the rotating border */
  .rotating-light-outline > * {
    position: relative;
    z-index: 1;
  }
}


/* Responsive utilities */
@layer utilities {
  .text-responsive {
    @apply text-sm sm:text-base lg:text-lg;
  }
  
  .heading-responsive {
    @apply text-xl sm:text-2xl lg:text-3xl xl:text-4xl;
  }
  
  .padding-responsive {
    @apply px-4 sm:px-6 lg:px-8;
  }
  
  .margin-responsive {
    @apply mx-4 sm:mx-6 lg:mx-8;
  }

  /* Prevent horizontal overflow */
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Fix container overflow */
  .container {
    max-width: 100%;
    overflow-x: hidden;
  }

  /* Ensure sections don't overflow */
  section {
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* Fix for components with internal scrolling */
  .scroll-container {
    overflow-y: auto;
    overflow-x: hidden;
    overscroll-behavior: contain;
    touch-action: pan-y;
  }

  /* Prevent textarea from interfering with page scroll */
  textarea {
    touch-action: pan-y;
    overscroll-behavior: contain;
  }

  /* Fix for select dropdowns */
  [data-radix-select-content] {
    overscroll-behavior: contain;
    touch-action: pan-y;
  }
}