// FormBold API integration for form submissions
const FORMBOLD_ENDPOINT = import.meta.env.VITE_FORMBOLD_ENDPOINT || 'https://formbold.com/s/YOUR_FORM_ID'

// Validate that the FormBold endpoint is configured
if (!import.meta.env.VITE_FORMBOLD_ENDPOINT || import.meta.env.VITE_FORMBOLD_ENDPOINT === 'https://formbold.com/s/YOUR_FORM_ID') {
  console.warn('⚠️ FormBold endpoint not configured. Please set VITE_FORMBOLD_ENDPOINT in your .env file.')
}

export interface ContactFormData {
  name: string
  email: string
  phone?: string
  message: string
}

export interface BookingFormData {
  pickupLocation: string
  dropoffLocation: string
  date: string
  time: string
  passengers: number
  cabType: string
  customerName: string
  customerPhone: string
  customerEmail: string
  specialRequests?: string
}

export interface FeedbackFormData {
  customerName: string
  customerEmail: string
  customerPhone?: string
  rating: number
  serviceType: string
  message: string
}

export interface NewsletterFormData {
  email: string
}

export const formBoldService = {
  async submitContactForm(data: ContactFormData) {
    try {
      const response = await fetch(FORMBOLD_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          formType: 'contact'
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to submit contact form')
      }

      return await response.json()
    } catch (error) {
      console.error('Contact form submission error:', error)
      throw error
    }
  },

  async submitBookingForm(data: BookingFormData) {
    try {
      const response = await fetch(FORMBOLD_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          formType: 'booking'
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to submit booking form')
      }

      return await response.json()
    } catch (error) {
      console.error('Booking form submission error:', error)
      throw error
    }
  },

  async submitFeedbackForm(data: FeedbackFormData) {
    try {
      const response = await fetch(FORMBOLD_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          formType: 'feedback'
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to submit feedback form')
      }

      return await response.json()
    } catch (error) {
      console.error('Feedback form submission error:', error)
      throw error
    }
  },

  async submitNewsletterForm(data: NewsletterFormData) {
    try {
      const response = await fetch(FORMBOLD_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          formType: 'newsletter'
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to submit newsletter subscription')
      }

      return await response.json()
    } catch (error) {
      console.error('Newsletter subscription error:', error)
      throw error
    }
  }
}