import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { Button } from "../../../../components/ui/button";
import { Card, CardContent } from "../../../../components/ui/card";
import { FadeInUp } from "../../../../components/animations/FadeInUp";
import { StaggerContainer } from "../../../../components/animations/StaggerContainer";
import { StaggerItem } from "../../../../components/animations/StaggerItem";

import { vehicleService } from "../../../../lib/database";
import type { Vehicle, VehicleType } from "../../../../lib/database";

export const ChooseYourRideSection = (): JSX.Element => {
  const navigate = useNavigate();
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [vehiclesByType, setVehiclesByType] = useState<Record<VehicleType, Vehicle[]>>({
    economy: [],
    premium: [],
    luxury: [],
    traveller: []
  });

  useEffect(() => {
    const loadVehicles = async () => {
      try {
        const allVehicles = await vehicleService.getAllVehicles();
        setVehicles(allVehicles);
        
        // Group vehicles by type
        const grouped = allVehicles.reduce((acc, vehicle) => {
          if (!acc[vehicle.type]) {
            acc[vehicle.type] = [];
          }
          acc[vehicle.type].push(vehicle);
          return acc;
        }, {} as Record<VehicleType, Vehicle[]>);
        
        setVehiclesByType(grouped);
      } catch (error) {
        console.error('Error loading vehicles:', error);
      }
    };

    loadVehicles();
  }, []);

  // Feature cards data
  const features = [
    {
      title: "24/7 Availability",
      icon: "https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=32&h=32&fit=crop",
      description: [
        "Our cab service is available",
        "round the clock to ensure you",
        "can get a ride whenever you",
        "need one.",
      ],
    },
    {
      title: "Safe & Secure",
      icon: "https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=32&h=32&fit=crop",
      description: [
        "Your safety is our priority. All",
        "our rides are monitored and our",
        "drivers are thoroughly vetted.",
      ],
    },
    {
      title: "Real-time Tracking",
      icon: "https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=32&h=32&fit=crop",
      description: [
        "Track your cab in real-time with",
        "our advanced GPS system.",
        "Know exactly when your ride",
        "will arrive.",
      ],
    },
    {
      title: "Professional Drivers",
      icon: "https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=32&h=32&fit=crop",
      description: [
        "Our drivers are experienced",
        "professionals who provide",
        "courteous and efficient service.",
      ],
    },
  ];

  // Ride options data
  const rideOptions = [
    {
      type: 'economy' as VehicleType,
      title: "Economy",
      image: "https://images.pexels.com/photos/1545743/pexels-photo-1545743.jpeg?auto=compress&cs=tinysrgb&w=400&h=200&fit=crop",
      description: "Affordable rides for everyday travel.",
      buttonText: "Book Economy",
      route: "/book/economy",
    },
    {
      type: 'premium' as VehicleType,
      title: "Premium",
      image: "https://images.pexels.com/photos/3802510/pexels-photo-3802510.jpeg?auto=compress&cs=tinysrgb&w=400&h=200&fit=crop",
      description: "Enhanced comfort with newer vehicles.",
      buttonText: "Book Premium",
      route: "/book/premium",
    },
    {
      type: 'luxury' as VehicleType,
      title: "Luxury",
      image: "https://images.pexels.com/photos/3802508/pexels-photo-3802508.jpeg?auto=compress&cs=tinysrgb&w=400&h=200&fit=crop",
      description: "High-end vehicles with premium amenities.",
      buttonText: "Book Luxury",
      route: "/book/luxury",
    },
    {
      type: 'traveller' as VehicleType,
      title: "Traveller",
      image: "https://images.pexels.com/photos/1592384/pexels-photo-1592384.jpeg?auto=compress&cs=tinysrgb&w=400&h=200&fit=crop",
      description: "Spacious vehicles for groups or luggage.",
      buttonText: "Book Traveller",
      route: "/book/traveller",
    },
  ];

  return (
    <section className="w-full py-12 lg:py-24 relative overflow-x-hidden prevent-scroll-interference">
      <div className="container mx-auto px-4 max-w-7xl">
        {/* Top description */}
        <FadeInUp>
          <div className="max-w-2xl mx-auto mb-12 lg:mb-16 text-center">
            <p className="text-base text-gray-400 mb-2">
              Experience the difference with our comprehensive range of cab services
              designed to meet all your transportation needs.
            </p>
          </div>
        </FadeInUp>

        {/* Features section */}
        <StaggerContainer className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12 lg:mb-16">
          {features.map((feature, index) => (
            <StaggerItem key={index}>
              <Card className="bg-[#060e23] rounded-xl shadow-[0px_1px_2px_#0000000d] hover:bg-[#0a1428] transition-all duration-300 border border-[#1f293780] hover:border-[#e9a319]/30 rotating-light-outline">
                  <CardContent className="p-6 text-center flex flex-col">
                    <div className="flex flex-col items-center mb-6">
                      <motion.div
                        className="w-14 h-14 bg-[#e9a31933] rounded-full flex items-center justify-center mb-6 hover:bg-[#e9a31950] transition-colors duration-300"
                      >
                        <img
                          className="w-7 h-7 rounded-full object-cover"
                          alt={`${feature.title} icon`}
                          src={feature.icon}
                        />
                      </motion.div>
                      <h3 className="font-normal text-lg text-gray-50 mb-6">
                        {feature.title}
                      </h3>
                    </div>
                    <div className="flex-1">
                      {feature.description.map((line, i) => (
                        <p
                          key={i}
                          className="font-normal text-gray-400 text-sm leading-5"
                        >
                          {line}
                        </p>
                      ))}
                    </div>
                  </CardContent>
                </Card>
            </StaggerItem>
          ))}
        </StaggerContainer>

        {/* Middle description */}
        <FadeInUp>
          <div className="max-w-2xl mx-auto mb-12 lg:mb-16 text-center">
            <p className="text-base text-gray-400 mb-2">
              Select from our fleet of vehicles to match your needs, whether you're
            </p>
            <p className="text-base text-gray-400">
              traveling for business or pleasure.
            </p>
          </div>
        </FadeInUp>

        {/* Ride options section */}
        <StaggerContainer className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {rideOptions.map((ride, index) => (
            <StaggerItem key={index}>
              <Card className="bg-[#060e23] rounded-xl shadow-[0px_1px_2px_#0000000d] hover:bg-[#0a1428] transition-all duration-300 border border-[#1f293780] hover:border-[#e9a319]/30 group flex flex-col rotating-light-outline">
                  <CardContent className="p-0 flex flex-col">
                    <div className="relative h-48 w-full">
                      <motion.img
                        src={vehiclesByType[ride.type]?.[0]?.image_url || ride.image}
                        alt={ride.title}
                        className="w-full h-full object-cover transition-all duration-500"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent">
                        <h3 className="absolute bottom-4 left-4 font-normal text-white text-xl">
                          {ride.title}
                        </h3>
                      </div>
                    </div>
                    <div className="p-4 flex-1 flex flex-col">
                      <p className="font-normal text-gray-400 text-sm leading-5 mb-4 flex-1">
                        {ride.description} 
                        {vehiclesByType[ride.type]?.length > 0 && 
                          ` (${vehiclesByType[ride.type].length} available)`
                        }
                      </p>
                      {vehiclesByType[ride.type]?.length > 0 && (
                        <p className="text-xs text-[#e9a319] mb-2">
                          Starting from ₹{Math.min(...vehiclesByType[ride.type].map(v => v.price_per_km))}/km
                        </p>
                      )}
                      <Button
                        variant="outline"
                        className={`w-full h-10 bg-[#030711] border-[#8e919666] text-gray-50 text-sm font-medium hover:bg-[#e9a319] hover:text-black hover:border-[#e9a319] transition-all duration-300 hover:scale-105 ${
                          vehiclesByType[ride.type]?.length === 0 ? 'opacity-50 cursor-not-allowed' : ''
                        }`}
                        onClick={() => navigate(ride.route)}
                        disabled={vehiclesByType[ride.type]?.length === 0}
                      >
                        {vehiclesByType[ride.type]?.length === 0 ? 'Not Available' : ride.buttonText}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
            </StaggerItem>
          ))}
        </StaggerContainer>
      </div>
    </section>
  );
};