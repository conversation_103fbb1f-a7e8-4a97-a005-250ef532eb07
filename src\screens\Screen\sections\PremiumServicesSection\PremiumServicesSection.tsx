import { CalendarIcon, ClockIcon } from "lucide-react";
import React from "react";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { Badge } from "../../../../components/ui/badge";
import { But<PERSON> } from "../../../../components/ui/button";
import { Card, CardContent, CardTitle } from "../../../../components/ui/card";
import { Separator } from "../../../../components/ui/separator";
import { FadeInUp } from "../../../../components/animations/FadeInUp";
import { StaggerContainer } from "../../../../components/animations/StaggerContainer";
import { StaggerItem } from "../../../../components/animations/StaggerItem";
import { ScaleOnHover } from "../../../../components/animations/ScaleOnHover";
import { blogService } from "../../../../lib/database";
import type { BlogPost } from "../../../../lib/database";

export const PremiumServicesSection = (): JSX.Element => {
  const navigate = useNavigate();
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadBlogPosts = async () => {
      try {
        const posts = await blogService.getAllPublishedPosts();
        setBlogPosts(posts.slice(0, 3)); // Get first 3 posts
      } catch (error) {
        console.error('Error loading blog posts:', error);
      } finally {
        setLoading(false);
      }
    };

    loadBlogPosts();
  }, []);

  if (loading || blogPosts.length === 0) {
    return (
      <section className="w-full py-12 lg:py-16 relative overflow-x-hidden prevent-scroll-interference">
        <div className="container mx-auto px-4 max-w-7xl">
          <FadeInUp>
            <div className="flex flex-col items-center text-center mb-12 lg:mb-14">
              <h2 className="text-3xl lg:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-yellow-400 to-amber-600">
                Travel Insights
              </h2>
              <p className="text-lg text-gray-400 max-w-2xl">
                {loading ? 'Loading latest travel insights...' : 'No blog posts available at the moment.'}
              </p>
            </div>
          </FadeInUp>
        </div>
      </section>
    );
  }

  const articles = blogPosts.slice(1, 3); // Get 2 articles for the grid
  const featuredArticle = blogPosts[0]; // First article as featured

  const handleArticleClick = (articleId: string) => {
    navigate(`/blog/${articleId}`);
  };

  return (
    <section className="w-full py-12 lg:py-16 relative overflow-x-hidden prevent-scroll-interference">
      <div className="container mx-auto px-4 max-w-7xl">
        <FadeInUp>
          <div className="flex flex-col items-center text-center mb-12 lg:mb-14">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-yellow-400 to-amber-600">
              Travel Insights
            </h2>
            <p className="text-lg text-gray-400 max-w-2xl">
              Stay updated with the latest travel tips, destination guides, and
              news from Bhoomi Tour & Travels.
            </p>
          </div>
        </FadeInUp>

        <FadeInUp delay={0.2}>
          <ScaleOnHover scale={1.02}>
            <Card 
              className="mb-12 bg-[#060e23cc] border-[#1f293780] overflow-hidden rounded-xl cursor-pointer hover:bg-[#0a1428] transition-all duration-300 hover:border-[#e9a319]/30 group"
              onClick={() => handleArticleClick(featuredArticle.id)}
            >
              <div className="flex flex-col lg:flex-row">
                <div className="w-full lg:w-1/2 h-64 lg:h-auto min-h-[300px] lg:min-h-[400px] overflow-hidden relative">
                  <motion.img
                    src={featuredArticle.image_url || 'https://images.pexels.com/photos/1591373/pexels-photo-1591373.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop'}
                    alt={featuredArticle.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                  />
                  <div className="absolute top-3.5 left-3">
                    <Badge className="bg-[#e9a319cc] text-white font-medium">
                      {featuredArticle.category}
                    </Badge>
                  </div>
                </div>

                <CardContent className="w-full lg:w-1/2 p-6 lg:p-8 flex flex-col justify-between">
                  <div className="space-y-4">
                    <CardTitle className="text-xl lg:text-2xl font-bold text-gray-50 leading-7 group-hover:text-[#e9a319] transition-colors">
                      {featuredArticle.title}
                    </CardTitle>
                    <p className="text-sm text-gray-400 line-clamp-3">
                      {featuredArticle.excerpt || featuredArticle.content.substring(0, 200) + '...'}
                    </p>
                  </div>

                  <div className="mt-8">
                    <div className="flex items-center gap-6 mb-3">
                      <div className="flex items-center gap-1.5">
                        <CalendarIcon className="w-3.5 h-3.5 text-gray-400" />
                        <span className="text-xs text-gray-400">
                          {featuredArticle.published_at ? new Date(featuredArticle.published_at).toLocaleDateString() : 'Not published'}
                        </span>
                      </div>
                      <div className="flex items-center gap-1.5">
                        <ClockIcon className="w-3.5 h-3.5 text-gray-400" />
                        <span className="text-xs text-gray-400">
                          5 min read
                        </span>
                      </div>
                    </div>

                    <Separator className="my-3 bg-[#1f29374c]" />

                    <div className="flex items-center gap-2 mt-3 flex-wrap">
                      <svg className="w-3.5 h-3.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                      </svg>
                      {(featuredArticle.tags || []).slice(0, 2).map((tag, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className="bg-[#1f293780] text-gray-400 font-normal rounded text-xs"
                        >
                          {tag}
                        </Badge>
                      ))}
                      {(featuredArticle.tags || []).length > 2 && (
                        <span className="text-xs text-gray-400 ml-2">+{featuredArticle.tags.length - 2}</span>
                      )}
                    </div>
                  </div>
                </CardContent>
              </div>
            </Card>
          </ScaleOnHover>
        </FadeInUp>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {articles.map((article, index) => (
            <FadeInUp key={article.id} delay={0.3 + index * 0.1}>
              <ScaleOnHover>
                <Card
                  className="bg-[#060e23cc] border-[#1f293780] overflow-hidden rounded-xl h-full cursor-pointer hover:bg-[#0a1428] transition-all duration-300 hover:border-[#e9a319]/30 group"
                  onClick={() => handleArticleClick(article.id)}
                >
                  <div className="h-52 w-full relative overflow-hidden">
                    <motion.img
                      src={article.image_url || 'https://images.pexels.com/photos/1591373/pexels-photo-1591373.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop'}
                      alt={article.title}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className="absolute top-3.5 left-3">
                      <Badge className="bg-[#e9a319cc] text-white font-medium">
                        {article.category}
                      </Badge>
                    </div>
                  </div>

                  <CardContent className="p-5 flex flex-col h-full">
                    <CardTitle className="text-lg font-bold text-gray-50 mb-4 leading-7 group-hover:text-[#e9a319] transition-colors">
                      {article.title}
                    </CardTitle>
                    <p className="text-sm text-gray-400 mb-6 flex-1 line-clamp-3">
                      {article.excerpt || article.content.substring(0, 150) + '...'}
                    </p>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-1.5">
                        <CalendarIcon className="w-3.5 h-3.5 text-gray-400" />
                        <span className="text-xs text-gray-400">
                          {article.published_at ? new Date(article.published_at).toLocaleDateString() : 'Not published'}
                        </span>
                      </div>
                      <div className="flex items-center gap-1.5">
                        <ClockIcon className="w-3.5 h-3.5 text-gray-400" />
                        <span className="text-xs text-gray-400">
                          5 min read
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </ScaleOnHover>
            </FadeInUp>
          ))}
        </div>

        <FadeInUp delay={0.4}>
          <div className="flex justify-center">
            <ScaleOnHover>
              <Button
                variant="outline"
                className="bg-[#030711] text-gray-50 border-gray-800 font-medium hover:bg-[#e9a319] hover:text-black hover:border-[#e9a319] transition-all duration-300"
                onClick={() => blogPosts.length > 0 && navigate(`/blog/${blogPosts[0].id}`)}
              >
                Explore All Articles
                <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Button>
            </ScaleOnHover>
          </div>
        </FadeInUp>
      </div>
    </section>
  );
};