import React from "react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { But<PERSON> } from "../../../../components/ui/button";
import { Input } from "../../../../components/ui/input";
import { Form, FormControl, FormField, FormItem, FormMessage } from "../../../../components/ui/form";
import { useToast } from "../../../../hooks/use-toast";
import { formBoldService } from "../../../../lib/formbold";
import { Separator } from "../../../../components/ui/separator";

const newsletterSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

type NewsletterFormData = z.infer<typeof newsletterSchema>;

export const FooterSection = (): JSX.Element => {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<NewsletterFormData>({
    resolver: zodResolver(newsletterSchema),
    defaultValues: {
      email: '',
    },
  });

  const onSubmit = async (data: NewsletterFormData) => {
    setIsSubmitting(true);

    try {
      await formBoldService.submitNewsletterForm(data);

      toast({
        title: "Subscription Successful!",
        description: "Thank you for subscribing to our newsletter. You'll receive updates and special offers.",
      });

      form.reset();
    } catch (error) {
      console.error('Newsletter subscription error:', error);
      toast({
        title: "Subscription Failed",
        description: "There was an error subscribing to our newsletter. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Company description data
  const companyDescription = [
    "Your trusted partner for premium cab",
    "services. We provide reliable,",
    "comfortable, and safe transportation",
    "solutions for all your needs.",
  ];

  // Quick links data
  const quickLinks = [
    "Home",
    "Services",
    "How It Works",
    "Testimonials",
    "Contact",
    "Book Now",
  ];

  // Contact information data
  const address = [
    "205, second floor, Vindhyachal",
    "complex, opposite Krishna Business",
    "tower, City Center, Tulsi Vihar",
    "Colony, Gwalior, Madhya Pradesh",
    "474004",
  ];

  const phoneNumbers = ["+91 7999475781", "+91 9301087764"];

  const email = "<EMAIL>";

  // Newsletter text
  const newsletterText = [
    "Subscribe to our newsletter to receive",
    "updates and special offers.",
  ];

  return (
    <footer className="w-full bg-gray-950 py-12 lg:py-16 px-4 overflow-hidden">
      <div className="container mx-auto max-w-7xl">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info Column */}
          <div className="flex flex-col space-y-6">
            <h2 className="font-bold text-xl lg:text-2xl text-white">
              Bhoomi Tours & Travels
            </h2>

            <div className="text-[#ffffffb2] space-y-1">
              {companyDescription.map((line, index) => (
                <p
                  key={index}
                  className="font-normal text-base leading-6"
                >
                  {line}
                </p>
              ))}
            </div>

            <div className="flex space-x-4">
              <div className="relative w-10 h-10 bg-[#ffffff1a] rounded-full flex items-center justify-center hover:bg-[#ffffff2a] transition-colors cursor-pointer">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                </svg>
              </div>
              <div className="relative w-10 h-10 bg-[#ffffff1a] rounded-full flex items-center justify-center hover:bg-[#ffffff2a] transition-colors cursor-pointer">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                </svg>
              </div>
            </div>
          </div>

          {/* Quick Links Column */}
          <div className="flex flex-col space-y-6">
            <h2 className="font-normal text-white text-lg leading-7">
              Quick Links
            </h2>

            <ul className="space-y-3">
              {quickLinks.map((link, index) => (
                <li key={index} className="flex items-center">
                  <div className="w-1.5 h-1.5 bg-[#e9a319] rounded-full mr-2 flex-shrink-0" />
                  <a
                    href="#"
                    className="font-normal text-[#ffffffb2] text-base leading-6 hover:text-white transition-colors"
                  >
                    {link}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Us Column */}
          <div className="flex flex-col space-y-6">
            <h2 className="font-normal text-white text-lg leading-7">
              Contact Us
            </h2>

            <div className="space-y-4">
              <div className="flex">
                <svg className="w-5 h-5 mt-0.5 mr-3 flex-shrink-0 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <div className="text-[#ffffffb2] min-w-0">
                  {address.map((line, index) => (
                    <p
                      key={index}
                      className="font-normal text-base leading-6"
                    >
                      {line}
                    </p>
                  ))}
                </div>
              </div>

              {phoneNumbers.map((phone, index) => (
                <div key={index} className="flex">
                  <svg className="w-5 h-5 mt-0.5 mr-3 flex-shrink-0 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                  <p className="font-normal text-[#ffffffb2] text-base leading-6">
                    {phone}
                  </p>
                </div>
              ))}

              <div className="flex">
                <svg className="w-5 h-5 mt-0.5 mr-3 flex-shrink-0 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <p className="font-normal text-[#ffffffb2] text-base leading-6 break-all">
                  {email}
                </p>
              </div>
            </div>
          </div>

          {/* Newsletter Column */}
          <div className="flex flex-col space-y-6">
            <h2 className="font-normal text-white text-lg leading-7">
              Newsletter
            </h2>

            <div className="text-[#ffffffb2]">
              {newsletterText.map((line, index) => (
                <p
                  key={index}
                  className="font-normal text-base leading-6"
                >
                  {line}
                </p>
              ))}
            </div>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          className="h-[50px] bg-[#ffffff1a] border-[#ffffff33] text-white font-normal text-base rounded-xl px-4 py-3.5 placeholder:text-gray-400"
                          placeholder="Your email address"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full h-12 bg-[#e9a319] hover:bg-[#d99100] text-white font-medium text-base rounded-xl"
                >
                  {isSubmitting ? 'Subscribing...' : 'Subscribe'}
                </Button>
              </form>
            </Form>
          </div>
        </div>

        <Separator className="my-8 bg-[#ffffff1a]" />

        <div className="flex justify-center">
          <p className="font-normal text-[#ffffff80] text-base leading-6 text-center">
            © 2025 Bhoomi Tour & Travels. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};