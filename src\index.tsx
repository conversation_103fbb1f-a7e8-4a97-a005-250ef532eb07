import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { Screen } from "./screens/Screen";
import { BookingPage } from "./pages/BookingPage";
import { BlogDetailPage } from "./pages/BlogDetailPage";
import { AdminPage } from "./pages/AdminPage";
import { AdminSetupPage } from "./pages/AdminSetupPage";
import { AdminFixPage } from "./pages/AdminFixPage";
import { Toaster } from "./components/ui/toaster";

createRoot(document.getElementById("app") as HTMLElement).render(
  <StrictMode>
    <Router>
      <Routes>
        <Route path="/" element={<Screen />} />
        <Route path="/book/:cabType" element={<BookingPage />} />
        <Route path="/blog/:blogId" element={<BlogDetailPage />} />
        <Route path="/admin" element={<AdminPage />} />
        <Route path="/admin/setup" element={<AdminSetupPage />} />
        <Route path="/admin/fix" element={<AdminFixPage />} />
        <Route path="/admin/debug" element={<AdminDebug />} />
      </Routes>
      <Toaster />
    </Router>
  </StrictMode>,
);