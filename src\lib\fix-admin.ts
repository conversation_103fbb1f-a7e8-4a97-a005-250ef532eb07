import { supabase } from './supabase'
import { userService } from './database'
import { testSupabaseConnection, quickAuthTest, quickDatabaseTest } from './connection-test'

export const fixAdminUser = async () => {
  try {
    console.log('🔧 Fixing admin user setup...')

    // Step 0: Test connection first
    console.log('🔍 Testing Supabase connection...')
    const connectionTest = await testSupabaseConnection()
    console.log('Connection test results:', connectionTest)

    if (!connectionTest.connection) {
      throw new Error(`Connection failed: ${connectionTest.details.databaseError || 'Unknown error'}`)
    }

    // Step 1: Try to sign in with existing admin credentials
    console.log('📝 Attempting to sign <NAME_EMAIL>...')
    
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'admin@123'
    })

    let authUserId: string | null = null

    if (signInError) {
      if (signInError.message.includes('Invalid login credentials')) {
        console.log('❌ Admin user does not exist, creating new one...')
        
        // Create new admin user
        const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
          email: '<EMAIL>',
          password: 'admin@123',
          options: {
            data: {
              name: 'Administrator'
            }
          }
        })

        if (signUpError) {
          throw new Error(`Failed to create admin user: ${signUpError.message}`)
        }

        if (!signUpData.user) {
          throw new Error('Failed to create admin user: No user returned')
        }

        authUserId = signUpData.user.id
        console.log('✅ Admin auth user created:', authUserId)
      } else {
        throw signInError
      }
    } else {
      authUserId = signInData.user?.id || null
      console.log('✅ Admin user signed in successfully:', authUserId)
    }

    if (!authUserId) {
      throw new Error('No auth user ID available')
    }

    // Step 2: Check if user profile exists in users table
    console.log('🔍 Checking user profile in database...')
    
    const { data: existingProfile, error: profileError } = await supabase
      .from('users')
      .select('*')
      .eq('auth_id', authUserId)
      .single()

    if (profileError && profileError.code !== 'PGRST116') {
      throw new Error(`Database error: ${profileError.message}`)
    }

    if (existingProfile) {
      console.log('📋 User profile exists:', existingProfile)
      
      // Update role to admin if not already
      if (existingProfile.role !== 'admin') {
        console.log('🔄 Updating user role to admin...')
        const { error: updateError } = await supabase
          .from('users')
          .update({ role: 'admin' })
          .eq('id', existingProfile.id)

        if (updateError) {
          throw new Error(`Failed to update user role: ${updateError.message}`)
        }
        console.log('✅ User role updated to admin')
      } else {
        console.log('✅ User already has admin role')
      }
    } else {
      console.log('📝 Creating new user profile...')
      
      // Create user profile
      const { error: createError } = await supabase
        .from('users')
        .insert({
          auth_id: authUserId,
          name: 'Administrator',
          phone: '+91 79994 75781',
          role: 'admin'
        })

      if (createError) {
        throw new Error(`Failed to create user profile: ${createError.message}`)
      }
      console.log('✅ User profile created successfully')
    }

    // Step 3: Verify the setup
    console.log('🔍 Verifying admin setup...')
    
    const { data: adminUser, error: verifyError } = await supabase
      .from('users')
      .select('*')
      .eq('auth_id', authUserId)
      .eq('role', 'admin')
      .single()

    if (verifyError) {
      throw new Error(`Verification failed: ${verifyError.message}`)
    }

    console.log('✅ Admin setup verified successfully!')
    console.log('📧 Email: <EMAIL>')
    console.log('🔑 Password: admin@123')
    console.log('👤 User Profile:', adminUser)

    return {
      success: true,
      message: 'Admin user setup completed successfully',
      user: adminUser
    }

  } catch (error) {
    console.error('❌ Admin setup failed:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error occurred',
      user: null
    }
  }
}

// Function to test admin login
export const testAdminLogin = async () => {
  try {
    console.log('🧪 Testing admin login...')
    
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'admin@123'
    })

    if (error) {
      throw error
    }

    if (data.user) {
      const userProfile = await userService.getCurrentUser()
      console.log('✅ Login test successful!')
      console.log('👤 User:', data.user.email)
      console.log('📋 Profile:', userProfile)
      
      return {
        success: true,
        isAdmin: userProfile?.role === 'admin',
        user: userProfile
      }
    }

    return { success: false, isAdmin: false, user: null }
  } catch (error) {
    console.error('❌ Login test failed:', error)
    return {
      success: false,
      isAdmin: false,
      user: null,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}
