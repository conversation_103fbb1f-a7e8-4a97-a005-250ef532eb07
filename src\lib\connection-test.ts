import { supabase } from './supabase'

export const testSupabaseConnection = async () => {
  const results = {
    connection: false,
    auth: false,
    database: false,
    tables: false,
    details: {} as any
  }

  try {
    // Test 1: Basic connection
    console.log('🔍 Testing Supabase connection...')
    const { data, error } = await supabase.from('users').select('count', { count: 'exact', head: true })
    
    if (!error) {
      results.connection = true
      results.database = true
      results.tables = true
      console.log('✅ Database connection successful')
    } else {
      console.error('❌ Database connection failed:', error)
      results.details.databaseError = error.message
    }

    // Test 2: Auth service
    try {
      const session = await supabase.auth.getSession()
      results.auth = true
      results.details.session = session.data.session ? 'Active' : 'None'
      console.log('✅ Auth service accessible')
    } catch (authError) {
      console.error('❌ Auth service failed:', authError)
      results.details.authError = authError instanceof Error ? authError.message : 'Unknown'
    }

    // Test 3: Environment variables
    results.details.environment = {
      hasUrl: !!import.meta.env.VITE_SUPABASE_URL,
      hasKey: !!import.meta.env.VITE_SUPABASE_ANON_KEY,
      url: import.meta.env.VITE_SUPABASE_URL?.substring(0, 30) + '...'
    }

    // Test 4: Table access
    try {
      const { data: usersTest } = await supabase.from('users').select('id').limit(1)
      const { data: vehiclesTest } = await supabase.from('vehicles').select('id').limit(1)
      const { data: blogsTest } = await supabase.from('blog_posts').select('id').limit(1)
      
      results.details.tables = {
        users: !!usersTest,
        vehicles: !!vehiclesTest,
        blogs: !!blogsTest
      }
    } catch (tableError) {
      results.details.tableError = tableError instanceof Error ? tableError.message : 'Unknown'
    }

  } catch (generalError) {
    console.error('❌ General connection test failed:', generalError)
    results.details.generalError = generalError instanceof Error ? generalError.message : 'Unknown'
  }

  return results
}

export const quickAuthTest = async () => {
  try {
    console.log('🧪 Quick auth test...')
    
    const startTime = Date.now()
    const session = await supabase.auth.getSession()
    const authTime = Date.now() - startTime
    
    console.log(`⏱️ Auth check took ${authTime}ms`)
    
    if (session.data.session) {
      console.log('✅ Active session found:', session.data.session.user.email)
      return { success: true, time: authTime, session: session.data.session }
    } else {
      console.log('ℹ️ No active session')
      return { success: true, time: authTime, session: null }
    }
  } catch (error) {
    console.error('❌ Auth test failed:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error',
      time: 0
    }
  }
}

export const quickDatabaseTest = async () => {
  try {
    console.log('🧪 Quick database test...')
    
    const startTime = Date.now()
    const { data, error } = await supabase
      .from('users')
      .select('count', { count: 'exact', head: true })
    
    const dbTime = Date.now() - startTime
    
    console.log(`⏱️ Database check took ${dbTime}ms`)
    
    if (error) {
      console.error('❌ Database test failed:', error)
      return { success: false, error: error.message, time: dbTime }
    } else {
      console.log('✅ Database accessible')
      return { success: true, time: dbTime, count: data }
    }
  } catch (error) {
    console.error('❌ Database test failed:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error',
      time: 0
    }
  }
}
