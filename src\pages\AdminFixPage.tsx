import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { useToast } from '../hooks/use-toast';
import { fixAdminUser, testAdminLogin } from '../lib/fix-admin';
import { Shield, CheckCircle, AlertCircle, Settings } from 'lucide-react';

export const AdminFixPage = () => {
  const [isFixing, setIsFixing] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [fixResult, setFixResult] = useState<any>(null);
  const [testResult, setTestResult] = useState<any>(null);
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleFixAdmin = async () => {
    setIsFixing(true);
    setFixResult(null);
    
    try {
      const result = await fixAdminUser();
      setFixResult(result);
      
      if (result.success) {
        toast({
          title: "Admin Setup Complete!",
          description: "Admin user has been set up successfully.",
        });
      } else {
        toast({
          title: "Setup Failed",
          description: result.message,
          variant: "destructive"
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setFixResult({
        success: false,
        message: errorMessage,
        user: null
      });
      
      toast({
        title: "Setup Error",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsFixing(false);
    }
  };

  const handleTestLogin = async () => {
    setIsTesting(true);
    setTestResult(null);
    
    try {
      const result = await testAdminLogin();
      setTestResult(result);
      
      if (result.success && result.isAdmin) {
        toast({
          title: "Login Test Successful!",
          description: "Admin login is working correctly.",
        });
      } else {
        toast({
          title: "Login Test Failed",
          description: result.error || "Login test failed",
          variant: "destructive"
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setTestResult({
        success: false,
        isAdmin: false,
        user: null,
        error: errorMessage
      });
      
      toast({
        title: "Test Error",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsTesting(false);
    }
  };

  const handleGoToAdmin = () => {
    navigate('/admin');
  };

  return (
    <div className="min-h-screen bg-[#030711] flex items-center justify-center p-4">
      <div className="w-full max-w-2xl space-y-6">
        {/* Header */}
        <Card className="bg-[#060e23] border-[#1f293780]">
          <CardHeader className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-amber-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <Settings className="w-8 h-8 text-white" />
            </div>
            <CardTitle className="text-2xl font-bold text-white">
              Admin Setup & Fix
            </CardTitle>
            <p className="text-gray-400">
              Fix admin login issues and verify setup
            </p>
          </CardHeader>
        </Card>

        {/* Admin Credentials */}
        <Card className="bg-[#060e23] border-[#1f293780]">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Shield className="w-5 h-5 mr-2 text-[#e9a319]" />
              Admin Credentials
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-[#1f293780] rounded-lg p-4 space-y-3">
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Email:</span>
                  <span className="text-[#e9a319] font-mono"><EMAIL></span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Password:</span>
                  <span className="text-[#e9a319] font-mono">admin@123</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <Card className="bg-[#060e23] border-[#1f293780]">
          <CardHeader>
            <CardTitle className="text-white">Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button
                onClick={handleFixAdmin}
                disabled={isFixing}
                className="bg-[#e9a319] text-black hover:bg-[#d99100] h-12"
              >
                {isFixing ? 'Fixing...' : 'Fix Admin Setup'}
              </Button>
              
              <Button
                onClick={handleTestLogin}
                disabled={isTesting}
                variant="outline"
                className="border-[#e9a319] text-[#e9a319] hover:bg-[#e9a319] hover:text-black h-12"
              >
                {isTesting ? 'Testing...' : 'Test Login'}
              </Button>
              
              <Button
                onClick={handleGoToAdmin}
                variant="outline"
                className="border-gray-600 text-gray-300 hover:bg-gray-600 hover:text-white h-12"
              >
                Go to Admin Panel
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Results */}
        {(fixResult || testResult) && (
          <Card className="bg-[#060e23] border-[#1f293780]">
            <CardHeader>
              <CardTitle className="text-white">Results</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {fixResult && (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    {fixResult.success ? (
                      <CheckCircle className="w-5 h-5 text-green-500" />
                    ) : (
                      <AlertCircle className="w-5 h-5 text-red-500" />
                    )}
                    <span className="text-white font-medium">Admin Setup:</span>
                    <span className={fixResult.success ? 'text-green-400' : 'text-red-400'}>
                      {fixResult.success ? 'Success' : 'Failed'}
                    </span>
                  </div>
                  <p className="text-gray-400 text-sm ml-7">{fixResult.message}</p>
                </div>
              )}
              
              {testResult && (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    {testResult.success && testResult.isAdmin ? (
                      <CheckCircle className="w-5 h-5 text-green-500" />
                    ) : (
                      <AlertCircle className="w-5 h-5 text-red-500" />
                    )}
                    <span className="text-white font-medium">Login Test:</span>
                    <span className={testResult.success && testResult.isAdmin ? 'text-green-400' : 'text-red-400'}>
                      {testResult.success && testResult.isAdmin ? 'Success' : 'Failed'}
                    </span>
                  </div>
                  {testResult.error && (
                    <p className="text-gray-400 text-sm ml-7">{testResult.error}</p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Instructions */}
        <Card className="bg-[#060e23] border-[#1f293780]">
          <CardHeader>
            <CardTitle className="text-white">Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-gray-400">
              <p>1. Click "Fix Admin Setup" to create/fix the admin user</p>
              <p>2. Click "Test Login" to verify the admin credentials work</p>
              <p>3. Once both are successful, go to the Admin Panel</p>
              <p>4. Use the credentials shown above to log in</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
