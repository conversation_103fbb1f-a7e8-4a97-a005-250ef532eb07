import React from "react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "../../../../components/ui/button";
import { Card, CardContent } from "../../../../components/ui/card";
import { Input } from "../../../../components/ui/input";
import { Textarea } from "../../../../components/ui/textarea";
import { Form, FormControl, FormField, FormItem, FormMessage } from "../../../../components/ui/form";
import { useToast } from "../../../../hooks/use-toast";
import { formBoldService } from "../../../../lib/formbold";

const contactSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Valid email required'),
  phone: z.string().optional(),
  message: z.string().min(1, 'Message is required'),
});

type ContactFormData = z.infer<typeof contactSchema>;

export const ContactSection = (): JSX.Element => {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<ContactFormData>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      message: '',
    },
  });

  const onSubmit = async (data: ContactFormData) => {
    setIsSubmitting(true);
    
    try {
      await formBoldService.submitContactForm(data);
      
      toast({
        title: "Message Sent!",
        description: "Thank you for your message. We'll get back to you soon.",
      });
      
      form.reset();
    } catch (error) {
      console.error('Contact form submission error:', error);
      toast({
        title: "Message Failed",
        description: "There was an error sending your message. Please try again or contact us directly.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Contact option cards data
  const contactOptions = [
    {
      title: "WhatsApp",
      description: "Quick chat with us",
      icon: "https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=32&h=32&fit=crop",
    },
    {
      title: "Phone",
      description: "+917999475781",
      icon: "https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=32&h=32&fit=crop",
    },
    {
      title: "Email",
      description: "<EMAIL>",
      icon: "https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=32&h=32&fit=crop",
    },
  ];

  return (
    <section className="w-full bg-[#030711] py-12 lg:py-16 overflow-x-hidden prevent-scroll-interference">
      <div className="container mx-auto px-4 max-w-6xl">
        {/* Section Header */}
        <div className="flex flex-col items-center justify-center mb-12 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            Get in Touch
          </h2>
          <p className="text-base text-gray-400">
            We're here to help! Choose your preferred way to reach us.
          </p>
        </div>

        {/* Main Content */}
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Contact Options Column */}
          <div className="w-full lg:w-1/2 flex flex-col gap-6">
            <h3 className="text-xl text-gray-50 leading-7">
              Contact Options
            </h3>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-4">
              {contactOptions.map((option, index) => (
                <Card
                  key={index}
                  className="bg-[#060e23] border-[#1f293780] shadow-[0px_1px_2px_#0000000d] h-full"
                >
                  <CardContent className="flex items-center p-4">
                    <img
                      className="w-6 h-6 mr-4 rounded-full object-cover"
                      alt={`${option.title} icon`}
                      src={option.icon}
                    />
                    <div className="flex flex-col min-w-0 flex-1">
                      <h4 className="text-base font-medium text-gray-50 leading-6 truncate">
                        {option.title}
                      </h4>
                      <p className="text-sm text-gray-400 truncate">
                        {option.description}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Contact Form Column */}
          <div className="w-full lg:w-1/2">
            <Card className="bg-[#060e23] border-[#1f293780] shadow-[0px_1px_2px_#0000000d]">
              <CardContent className="p-6">
                <h3 className="text-xl text-gray-50 leading-7 mb-6">
                  Send us a Message
                </h3>

                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col gap-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              placeholder="Your Name"
                              className="bg-[#030711] border-gray-800 text-gray-50 h-10 rounded-[10px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="Your Email"
                              className="bg-[#030711] border-gray-800 text-gray-50 h-10 rounded-[10px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              placeholder="Your Phone (optional)"
                              className="bg-[#030711] border-gray-800 text-gray-50 h-10 rounded-[10px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="message"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Textarea
                              placeholder="Your Message"
                              className="bg-[#030711] border-gray-800 text-gray-50 h-[120px] rounded-[10px] resize-none prevent-scroll-interference"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="bg-[#e9a319] text-black hover:bg-[#e9a319]/90 h-10 rounded-[10px]"
                    >
                      {isSubmitting ? 'Sending...' : 'Send Message'}
                    </Button>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};