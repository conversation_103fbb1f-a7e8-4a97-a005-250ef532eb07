import { motion } from "framer-motion";
import { ReactNode } from "react";

interface SlideInLeftProps {
  children: ReactNode;
  delay?: number;
  duration?: number;
  className?: string;
}

export const SlideInLeft = ({
  children,
  delay = 0,
  duration = 0.9,
  className = ""
}: SlideInLeftProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, x: -60 }}
      whileInView={{ opacity: 1, x: 0 }}
      viewport={{ once: true, margin: "-30px", amount: 0.2 }}
      transition={{
        duration,
        delay,
        ease: [0.25, 0.46, 0.45, 0.94],
        type: "spring",
        stiffness: 80,
        damping: 20,
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
};