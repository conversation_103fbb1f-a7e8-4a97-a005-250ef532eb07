import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { authService, userService } from '../lib/database';
import { Button } from './ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card';

export const AdminDebug = () => {
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [loading, setLoading] = useState(false);

  const runDiagnostics = async () => {
    setLoading(true);
    const info: any = {};

    try {
      // Check Supabase connection
      info.supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      info.hasAnonKey = !!import.meta.env.VITE_SUPABASE_ANON_KEY;

      // Check current session
      try {
        const session = await authService.getCurrentSession();
        info.session = {
          exists: !!session,
          userId: session?.user?.id,
          email: session?.user?.email,
        };
      } catch (error) {
        info.session = { error: error instanceof Error ? error.message : 'Unknown error' };
      }

      // Check users table
      try {
        const { data: users, error } = await supabase.from('users').select('*').limit(5);
        info.usersTable = {
          accessible: !error,
          count: users?.length || 0,
          error: error?.message,
          users: users?.map(u => ({ id: u.id, name: u.name, role: u.role, auth_id: u.auth_id }))
        };
      } catch (error) {
        info.usersTable = { error: error instanceof Error ? error.message : 'Unknown error' };
      }

      // Check admin users specifically
      try {
        const { data: adminUsers, error } = await supabase
          .from('users')
          .select('*')
          .eq('role', 'admin');
        info.adminUsers = {
          accessible: !error,
          count: adminUsers?.length || 0,
          error: error?.message,
          users: adminUsers
        };
      } catch (error) {
        info.adminUsers = { error: error instanceof Error ? error.message : 'Unknown error' };
      }

      // Test getCurrentUser function
      try {
        const currentUser = await userService.getCurrentUser();
        info.getCurrentUser = {
          success: true,
          user: currentUser
        };
      } catch (error) {
        info.getCurrentUser = { error: error instanceof Error ? error.message : 'Unknown error' };
      }

      // Check auth users
      try {
        const { data: authUsers, error } = await supabase.auth.admin.listUsers();
        info.authUsers = {
          accessible: !error,
          count: authUsers?.users?.length || 0,
          error: error?.message,
          users: authUsers?.users?.map(u => ({ id: u.id, email: u.email }))
        };
      } catch (error) {
        info.authUsers = { error: error instanceof Error ? error.message : 'Admin API not accessible' };
      }

    } catch (error) {
      info.generalError = error instanceof Error ? error.message : 'Unknown error';
    }

    setDebugInfo(info);
    setLoading(false);
  };

  const createTestAdmin = async () => {
    setLoading(true);
    try {
      // Try to create admin user
      const { data, error } = await supabase.auth.signUp({
        email: '<EMAIL>',
        password: 'admin@123',
      });

      if (error && !error.message.includes('already registered')) {
        throw error;
      }

      // If user already exists, try to sign in to get the user ID
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'admin@123'
      });

      if (signInError) {
        throw signInError;
      }

      if (signInData.user) {
        // Create or update user profile
        const { error: upsertError } = await supabase
          .from('users')
          .upsert({
            auth_id: signInData.user.id,
            name: 'Administrator',
            phone: '+91 79994 75781',
            role: 'admin'
          });

        if (upsertError) {
          throw upsertError;
        }

        alert('Admin user created/updated successfully!');
        runDiagnostics();
      }
    } catch (error) {
      alert(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    setLoading(false);
  };

  useEffect(() => {
    runDiagnostics();
  }, []);

  return (
    <div className="min-h-screen bg-[#030711] p-4">
      <div className="max-w-4xl mx-auto">
        <Card className="bg-[#060e23] border-[#1f293780] mb-6">
          <CardHeader>
            <CardTitle className="text-white">Admin Panel Debug Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-4">
              <Button 
                onClick={runDiagnostics} 
                disabled={loading}
                className="bg-[#e9a319] text-black hover:bg-[#d99100]"
              >
                {loading ? 'Running...' : 'Run Diagnostics'}
              </Button>
              <Button 
                onClick={createTestAdmin} 
                disabled={loading}
                variant="outline"
                className="border-[#e9a319] text-[#e9a319] hover:bg-[#e9a319] hover:text-black"
              >
                Create Test Admin
              </Button>
            </div>
            
            <div className="bg-[#1f293780] rounded-lg p-4">
              <pre className="text-gray-300 text-sm overflow-auto max-h-96">
                {JSON.stringify(debugInfo, null, 2)}
              </pre>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
