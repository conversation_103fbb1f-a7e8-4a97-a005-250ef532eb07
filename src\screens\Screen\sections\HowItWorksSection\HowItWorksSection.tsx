import React from "react";
import { Card, CardContent } from "../../../../components/ui/card";

export const HowItWorksSection = (): JSX.Element => {
  const steps = [
    {
      icon: "https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=32&h=32&fit=crop",
      title: "Enter Trip Details",
      description: [
        "Provide your pickup location, destination, date, and",
        "time using our easy-to-use online form.",
      ],
    },
    {
      icon: "https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=32&h=32&fit=crop",
      title: "Choose Your Vehicle",
      description: [
        "Browse available vehicle options and select the one",
        "that best suits your travel needs and budget.",
      ],
    },
    {
      icon: "https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=32&h=32&fit=crop",
      title: "Confirm Your Booking",
      description: [
        "Review your trip details, confirm your selection, and",
        "receive instant booking confirmation via email or SMS.",
      ],
    },
  ];

  return (
    <section className="w-full py-12 lg:py-24 bg-[#1a1f2c80] overflow-x-hidden prevent-scroll-interference">
      <div className="container mx-auto max-w-6xl px-4">
        <div className="flex flex-col items-center mb-12 lg:mb-14">
          <div className="max-w-2xl text-center">
            <p className="text-base text-gray-400 mb-6">
              Booking your next cab is quick and easy with our simple online
              process.
            </p>
            <p className="text-base text-gray-400">
              Follow these steps to book your ride:
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-10">
          {steps.map((step, index) => (
            <Card
              key={index}
              className="bg-transparent border-none h-full"
            >
              <CardContent className="p-0 flex flex-col items-center text-center h-full">
                <div className="w-20 h-20 rounded-full bg-[#1a1f2c] shadow-[0px_10px_15px_-3px_#0000001a,0px_4px_6px_-4px_#0000001a] flex items-center justify-center mb-6">
                  <img
                    className="w-8 h-8 rounded-full object-cover"
                    alt={`${step.title} icon`}
                    src={step.icon}
                  />
                </div>
                <h3 className="text-xl text-gray-50 font-normal mb-6">
                  {step.title}
                </h3>
                <div className="flex-1">
                  {step.description.map((line, lineIndex) => (
                    <p
                      key={lineIndex}
                      className="text-base text-gray-400 font-normal leading-6"
                    >
                      {line}
                    </p>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};