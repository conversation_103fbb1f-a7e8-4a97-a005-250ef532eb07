import { useState, useEffect } from 'react'
import { authService, userService } from '../lib/database'
import type { User } from '../lib/database'

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [session, setSession] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    let mounted = true;

    // Add timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      if (mounted) {
        console.warn('Auth initialization timeout - setting loading to false');
        setError('Authentication timeout - please refresh the page');
        setLoading(false);
      }
    }, 5000); // 5 second timeout (reduced from 10)

    // Get initial session
    const getInitialSession = async () => {
      try {
        console.log('🔄 Starting auth initialization...');
        setError(null)

        console.log('📡 Getting current session...');
        const session = await Promise.race([
          authService.getCurrentSession(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Session timeout')), 3000)
          )
        ]);

        if (!mounted) return;

        console.log('✅ Session retrieved:', session ? 'Found' : 'None');
        setSession(session)

        if (session?.user) {
          console.log('👤 Getting user profile for:', session.user.email);
          try {
            const userData = await Promise.race([
              userService.getCurrentUser(),
              new Promise((_, reject) =>
                setTimeout(() => reject(new Error('User profile timeout')), 3000)
              )
            ]);

            if (mounted) {
              console.log('✅ User profile loaded:', userData?.name || 'Unknown');
              setUser(userData)
            }
          } catch (userError) {
            console.error('❌ Error getting user profile:', userError)
            if (mounted) {
              setUser(null)
              setError(`Failed to load user profile: ${userError instanceof Error ? userError.message : 'Unknown error'}`)
            }
          }
        } else {
          console.log('ℹ️ No active session found');
          if (mounted) {
            setUser(null)
          }
        }
      } catch (error) {
        console.error('❌ Error getting initial session:', error)
        if (mounted) {
          setError(`Failed to initialize auth: ${error instanceof Error ? error.message : 'Unknown error'}`)
          setUser(null)
          setSession(null)
        }
      } finally {
        if (mounted) {
          console.log('🏁 Auth initialization complete');
          clearTimeout(timeoutId);
          setLoading(false)
        }
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = authService.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return;

        console.log('Auth state changed:', event, session?.user?.email)
        setSession(session)

        if (session?.user) {
          try {
            const userData = await userService.getCurrentUser()
            if (mounted) {
              setUser(userData)
            }
          } catch (error) {
            console.error('Error fetching user data:', error)
            if (mounted) {
              setError('Failed to fetch user data')
              setUser(null)
            }
          }
        } else {
          if (mounted) {
            setUser(null)
          }
        }

        if (mounted) {
          setLoading(false)
        }
      }
    )

    return () => {
      mounted = false;
      clearTimeout(timeoutId);
      subscription.unsubscribe();
    }
  }, [])

  const signUp = async (email: string, password: string, userData: { name: string; phone?: string }) => {
    setLoading(true)
    setError(null)
    try {
      const result = await authService.signUp(email, password, userData)
      return result
    } catch (error) {
      console.error('SignUp error:', error)
      setError(error instanceof Error ? error.message : 'Sign up failed')
      throw error
    } finally {
      setLoading(false)
    }
  }

  const signIn = async (email: string, password: string) => {
    setLoading(true)
    setError(null)
    try {
      const result = await authService.signIn(email, password)
      console.log('SignIn successful:', result.user?.email)
      return result
    } catch (error) {
      console.error('SignIn error:', error)
      setError(error instanceof Error ? error.message : 'Sign in failed')
      throw error
    } finally {
      setLoading(false)
    }
  }

  const signOut = async () => {
    setLoading(true)
    setError(null)
    try {
      await authService.signOut()
      setUser(null)
      setSession(null)
      console.log('SignOut successful')
    } catch (error) {
      console.error('SignOut error:', error)
      setError(error instanceof Error ? error.message : 'Sign out failed')
      throw error
    } finally {
      setLoading(false)
    }
  }

  return {
    user,
    session,
    loading,
    error,
    signUp,
    signIn,
    signOut,
    isAuthenticated: !!session,
    isAdmin: user?.role === 'admin'
  }
}